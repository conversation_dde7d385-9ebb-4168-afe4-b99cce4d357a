"""test_disable_decryption.py
Тест отключения расшифровки зашифрованных контактов
"""

import os
import sys
import asyncio
from unittest.mock import AsyncMock, MagicMock

# Предотвращаем запуск CLI
sys.argv = ['test']

def test_encrypted_contacts_filtering():
    """Тест фильтрации зашифрованных контактов"""
    print("\n🧪 ТЕСТ ФИЛЬТРАЦИИ ЗАШИФРОВАННЫХ КОНТАКТОВ")
    print("=" * 60)

    try:
        # Тестируем логику фильтрации без импорта функции
        print("📝 Тестируем логику определения зашифрованных контактов...")
        
        # Тестовые данные с зашифрованными и обычными контактами
        mock_response_data = {
            "data": [
                {
                    "first_name": "Александр",
                    "last_name": "Иванов", 
                    "position": "CTO",
                    "company": "Тест Компания"
                },
                {
                    "first_name": "А****",
                    "last_name": "П****",
                    "position": "CIO", 
                    "company": "Тест Компания"
                },
                {
                    "first_name": "М",
                    "last_name": "С****",
                    "position": "Директор ИТ",
                    "company": "Тест Компания"
                },
                {
                    "first_name": "Елена",
                    "last_name": "Сидорова",
                    "position": "Руководитель отдела",
                    "company": "Тест Компания"
                }
            ]
        }
        
        print(f"📊 Исходные данные:")
        print(f"  Всего контактов: {len(mock_response_data['data'])}")
        print(f"  Обычные контакты: Александр Иванов, Елена Сидорова")
        print(f"  Зашифрованные контакты: А**** П****, М С****")
        
        # Анализируем какие контакты должны быть отфильтрованы
        expected_filtered = []
        expected_encrypted = []
        
        for contact in mock_response_data['data']:
            first_name = contact.get("first_name", "")
            last_name = contact.get("last_name", "")
            
            is_partial_first = "*" in first_name or len(first_name) <= 2
            is_partial_last = "*" in last_name or len(last_name) <= 2
            
            if is_partial_first or is_partial_last:
                expected_encrypted.append(f"{last_name} {first_name}")
            else:
                expected_filtered.append(f"{last_name} {first_name}")
        
        print(f"\n📋 Ожидаемые результаты:")
        print(f"  Должны остаться: {expected_filtered}")
        print(f"  Должны быть пропущены: {expected_encrypted}")
        
        # Проверяем логику фильтрации
        filtered_count = len(expected_filtered)
        encrypted_count = len(expected_encrypted)
        
        print(f"\n✅ Проверка логики фильтрации:")
        print(f"  Ожидается обычных контактов: {filtered_count}")
        print(f"  Ожидается зашифрованных контактов: {encrypted_count}")
        print(f"  Процент отфильтрованных: {encrypted_count / len(mock_response_data['data']) * 100:.1f}%")
        
        # Проверяем правильность определения зашифрованных контактов
        test_cases = [
            ("Александр", "Иванов", False, "Обычный контакт"),
            ("А****", "П****", True, "Зашифрованное имя и фамилия"),
            ("М", "С****", True, "Короткое имя и зашифрованная фамилия"),
            ("Елена", "Сидорова", False, "Обычный контакт"),
            ("А", "Петров", True, "Слишком короткое имя"),
            ("Анна", "С*", True, "Зашифрованная фамилия"),
        ]
        
        print(f"\n🔍 Тестирование определения зашифрованных контактов:")
        all_correct = True
        for first_name, last_name, expected_encrypted, description in test_cases:
            is_partial_first = "*" in first_name or len(first_name) <= 2
            is_partial_last = "*" in last_name or len(last_name) <= 2
            is_encrypted = is_partial_first or is_partial_last
            
            status = "✅" if is_encrypted == expected_encrypted else "❌"
            print(f"  {status} {last_name} {first_name}: {description} -> {'Зашифрован' if is_encrypted else 'Обычный'}")
            
            if is_encrypted != expected_encrypted:
                all_correct = False
        
        return all_correct and filtered_count == 2 and encrypted_count == 2
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_process_single_contact_with_encrypted():
    """Тест обработки зашифрованного контакта"""
    print("\n🧪 ТЕСТ ОБРАБОТКИ ЗАШИФРОВАННОГО КОНТАКТА")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import process_single_contact_with_email
        
        # Мокаем OpenAI клиент
        mock_client = AsyncMock()
        
        # Тестовые данные - зашифрованный контакт
        encrypted_contact = {
            "first_name": "А****",
            "last_name": "П****",
            "position": "CTO",
            "source": "setka_api"
        }
        
        # Тестовые данные - обычный контакт
        normal_contact = {
            "first_name": "Александр",
            "last_name": "Петров",
            "position": "CTO", 
            "source": "setka_api"
        }
        
        print(f"📝 Тестирование обработки контактов...")
        
        async def run_test():
            # Тест зашифрованного контакта
            print(f"  🔒 Тестируем зашифрованный контакт: А**** П****")
            result_encrypted = await process_single_contact_with_email(
                mock_client, 
                "Тест Компания", 
                encrypted_contact, 
                "<EMAIL>"
            )
            
            # Тест обычного контакта (мокаем GPT ответ)
            print(f"  👤 Тестируем обычный контакт: Александр Петров")
            mock_client.chat.completions.create.return_value = AsyncMock()
            mock_client.chat.completions.create.return_value.choices = [
                MagicMock(message=MagicMock(content="<EMAIL>"))
            ]
            
            result_normal = await process_single_contact_with_email(
                mock_client,
                "Тест Компания",
                normal_contact,
                "<EMAIL>"
            )
            
            return result_encrypted, result_normal
        
        # Запускаем асинхронный тест
        result_encrypted, result_normal = asyncio.run(run_test())
        
        print(f"\n📊 Результаты обработки:")
        print(f"  Зашифрованный контакт: '{result_encrypted}'")
        print(f"  Обычный контакт: '{result_normal}'")
        
        # Проверяем результаты
        encrypted_skipped = result_encrypted == ""
        normal_processed = result_normal != ""
        
        print(f"\n📋 Проверки:")
        print(f"  ✅ Зашифрованный контакт пропущен: {encrypted_skipped}")
        print(f"  ✅ Обычный контакт обработан: {normal_processed}")
        
        return encrypted_skipped and normal_processed
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_functions_removed():
    """Тест удаления функций расшифровки"""
    print("\n🧪 ТЕСТ УДАЛЕНИЯ ФУНКЦИЙ РАСШИФРОВКИ")
    print("=" * 60)
    
    try:
        import leadgen_enhanced
        
        # Проверяем, что функции расшифровки удалены
        functions_to_check = [
            "decrypt_setka_contacts",
            "resolve_partial_name"
        ]
        
        print(f"🔍 Проверяем удаление функций расшифровки...")
        
        removed_functions = []
        existing_functions = []
        
        for func_name in functions_to_check:
            if hasattr(leadgen_enhanced, func_name):
                existing_functions.append(func_name)
                print(f"  ❌ Функция {func_name} все еще существует")
            else:
                removed_functions.append(func_name)
                print(f"  ✅ Функция {func_name} удалена")
        
        # Проверяем, что в коде нет вызовов удаленных функций
        print(f"\n📄 Проверяем исходный код на наличие вызовов...")
        
        with open('leadgen_enhanced.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        problematic_calls = []
        for func_name in functions_to_check:
            if f"await {func_name}(" in source_code or f"{func_name}(" in source_code:
                problematic_calls.append(func_name)
                print(f"  ⚠️ Найден вызов удаленной функции: {func_name}")
            else:
                print(f"  ✅ Вызовы функции {func_name} не найдены")
        
        print(f"\n📊 Итоги проверки:")
        print(f"  Удаленные функции: {removed_functions}")
        print(f"  Оставшиеся функции: {existing_functions}")
        print(f"  Проблемные вызовы: {problematic_calls}")
        
        return len(existing_functions) == 0 and len(problematic_calls) == 0
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def test_setka_api_integration():
    """Тест интеграции с Setka API без расшифровки"""
    print("\n🧪 ТЕСТ ИНТЕГРАЦИИ С SETKA API БЕЗ РАСШИФРОВКИ")
    print("=" * 60)
    
    try:
        # Проверяем, что в коде есть правильная логика фильтрации
        with open('leadgen_enhanced.py', 'r', encoding='utf-8') as f:
            source_code = f.read()
        
        # Ищем ключевые фразы новой логики
        key_phrases = [
            "Фильтруем зашифрованные контакты",
            "Пропускаем зашифрованный контакт",
            "encrypted_count",
            "filtered_contacts"
        ]
        
        print(f"🔍 Проверяем наличие новой логики фильтрации...")
        
        found_phrases = []
        missing_phrases = []
        
        for phrase in key_phrases:
            if phrase in source_code:
                found_phrases.append(phrase)
                print(f"  ✅ Найдена фраза: '{phrase}'")
            else:
                missing_phrases.append(phrase)
                print(f"  ❌ Не найдена фраза: '{phrase}'")
        
        # Проверяем, что старая логика расшифровки удалена
        old_phrases = [
            "Найдено.*зашифрованных контактов, попытка расшифровки",
            "decrypted_contacts"
        ]
        
        print(f"\n🗑️ Проверяем удаление старой логики...")
        
        old_found = []
        old_removed = []
        
        import re
        for phrase in old_phrases:
            if re.search(phrase, source_code):
                old_found.append(phrase)
                print(f"  ⚠️ Найдена старая фраза: '{phrase}'")
            else:
                old_removed.append(phrase)
                print(f"  ✅ Удалена старая фраза: '{phrase}'")
        
        print(f"\n📊 Результаты проверки кода:")
        print(f"  Новая логика найдена: {len(found_phrases)}/{len(key_phrases)}")
        print(f"  Старая логика удалена: {len(old_removed)}/{len(old_phrases)}")
        
        return len(missing_phrases) == 0 and len(old_found) == 0  # Старая логика должна быть полностью удалена
        
    except Exception as e:
        print(f"❌ Ошибка теста: {e}")
        return False

def main():
    """Главная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ОТКЛЮЧЕНИЯ РАСШИФРОВКИ КОНТАКТОВ")
    print("=" * 80)
    
    # Тесты
    tests = [
        ("Фильтрация зашифрованных контактов", test_encrypted_contacts_filtering()),
        ("Обработка зашифрованного контакта", test_process_single_contact_with_encrypted()),
        ("Удаление функций расшифровки", test_functions_removed()),
        ("Интеграция с Setka API", test_setka_api_integration())
    ]
    
    print("\n📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:")
    print("=" * 80)
    
    passed = 0
    for test_name, result in tests:
        status = "✅ ПРОЙДЕН" if result else "❌ НЕ ПРОЙДЕН"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    total_tests = len(tests)
    print(f"\n🎯 ИТОГО: {passed}/{total_tests} тестов пройдено")
    
    if passed == total_tests:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!")
        print("\n✅ РАСШИФРОВКА КОНТАКТОВ ОТКЛЮЧЕНА:")
        print("  • Зашифрованные контакты пропускаются")
        print("  • Функции расшифровки удалены")
        print("  • Нет попыток расшифровки через GPT")
        print("  • Обрабатываются только открытые контакты")
        print("  • Улучшена производительность")
        print("  • Снижены затраты на API")
    elif passed > 2:
        print("⚠️  Частично работает - проверьте удаление функций")
    else:
        print("❌ Есть серьезные проблемы с отключением расшифровки")

if __name__ == "__main__":
    main()
