#!/usr/bin/env python3
"""
Тест генерации TXT файлов для клиентских менеджеров
"""

import os
import sys
import tempfile
from pathlib import Path

# Добавляем текущую директорию в путь для импорта
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_txt_functions():
    """Тест функций создания TXT документов"""
    print("🧪 ТЕСТ ФУНКЦИЙ СОЗДАНИЯ TXT ДОКУМЕНТОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import (
            create_txt_document_for_company,
            extract_emails_from_contacts,
            create_am_txt_files
        )
        
        # Тестовые данные
        company_name = "Тестовая Компания ООО"
        
        company_profile = """### 1. Основные ML/AI‑проекты
• Проект автоматизации документооборота с использованием NLP
• Система предиктивной аналитики для прогнозирования спроса
• Внедрение чат-бота для клиентского сервиса

### 2. Контакты LPR + телефоны/e‑mail
• Иванов Иван Иванович — Генеральный директор (<EMAIL>, +7 495 123-45-67)
• Петрова Мария Сергеевна — IT-директор (<EMAIL>, +7 495 123-45-68)
• Сидоров Петр Александрович — Руководитель отдела инноваций (<EMAIL>)

### 3. Релевантные закупки с kontur.zakupki
• Закупка системы машинного обучения. Дата: 15.03.2024. НМЦ: 5000000 руб.
• Разработка ИИ-решения для анализа данных. Дата: 20.02.2024. НМЦ: 3500000 руб."""

        email_content = """Добрый день!

Меня зовут Александр, я представляю компанию Nova AI.

Изучив деятельность Тестовая Компания ООО, мы видим активное развитие ИИ-инициатив.

Наша платформа поможет:

• автоматизировать документооборот более эффективно;
• улучшить качество предиктивной аналитики;
• расширить возможности чат-бота.

Предлагаю встречу для обсуждения сотрудничества.

С уважением,
Александр Смирнов
Nova AI"""

        # Тест 1: Извлечение email адресов
        print("\n1. Тест извлечения email адресов:")
        contacts_section = """• Иванов Иван Иванович — Генеральный директор (<EMAIL>, +7 495 123-45-67)
• Петрова Мария Сергеевна — IT-директор (<EMAIL>, +7 495 123-45-68)
• Сидоров Петр Александрович — Руководитель отдела инноваций (<EMAIL>)"""
        
        emails = extract_emails_from_contacts(contacts_section)
        print(f"   Найдено email адресов: {len(emails)}")
        for email in emails:
            print(f"   • {email}")
        
        # Тест 2: Создание TXT контента для компании
        print("\n2. Тест создания TXT контента для компании:")
        txt_content = create_txt_document_for_company(email_content, company_profile, company_name)
        print(f"   Длина контента: {len(txt_content)} символов")
        print("   Первые 200 символов:")
        print(f"   {txt_content[:200]}...")
        
        # Тест 3: Создание TXT файлов для АМ
        print("\n3. Тест создания TXT файлов для АМ:")
        
        # Подготавливаем тестовые данные
        companies_data = [
            ("Тестовая Компания 1", "1234567890", "Менеджер Иванов", company_profile, email_content),
            ("Тестовая Компания 2", "0987654321", "Менеджер Иванов", company_profile, email_content),
            ("Другая Компания", "1111111111", "Менеджер Петров", company_profile, email_content),
        ]
        
        # Создаем временную директорию
        with tempfile.TemporaryDirectory() as temp_dir:
            create_am_txt_files(companies_data, temp_dir)
            
            # Проверяем созданные файлы
            txt_files = list(Path(temp_dir).glob("*.txt"))
            print(f"   Создано файлов: {len(txt_files)}")
            
            for txt_file in txt_files:
                print(f"   • {txt_file.name}")
                with open(txt_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"     Размер: {len(content)} символов")
                    lines = content.split('\n')
                    print(f"     Строк: {len(lines)}")
        
        print("\n✅ Все тесты прошли успешно!")
        return True
        
    except Exception as e:
        print(f"\n❌ Ошибка в тестах: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_txt_template_reading():
    """Тест чтения TXT шаблонов"""
    print("\n🧪 ТЕСТ ЧТЕНИЯ TXT ШАБЛОНОВ")
    print("=" * 60)
    
    try:
        from leadgen_enhanced import read_txt_content
        
        # Проверяем чтение созданных шаблонов
        template_path = "template_email.txt"
        nova_ai_path = "nova_ai_positioning.txt"
        
        if os.path.exists(template_path):
            template_content = read_txt_content(template_path)
            print(f"✓ Шаблон письма загружен: {len(template_content)} символов")
            print(f"  Первые 100 символов: {template_content[:100]}...")
        else:
            print(f"❌ Файл шаблона не найден: {template_path}")
            
        if os.path.exists(nova_ai_path):
            nova_ai_content = read_txt_content(nova_ai_path)
            print(f"✓ Позиционирование Nova AI загружено: {len(nova_ai_content)} символов")
            print(f"  Первые 100 символов: {nova_ai_content[:100]}...")
        else:
            print(f"❌ Файл позиционирования не найден: {nova_ai_path}")
            
        return True
        
    except Exception as e:
        print(f"❌ Ошибка чтения шаблонов: {e}")
        return False

if __name__ == "__main__":
    print("🚀 ЗАПУСК ТЕСТОВ TXT ГЕНЕРАЦИИ")
    print("=" * 80)
    
    success = True
    
    # Запускаем тесты
    success &= test_txt_functions()
    success &= test_txt_template_reading()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО!")
        print("\nТеперь вы можете использовать систему с TXT шаблонами:")
        print("python3 leadgen_enhanced.py \\")
        print("  --excel companies.xlsx \\")
        print("  --template template_email.txt \\")
        print("  --nova-ai nova_ai_positioning.txt \\")
        print("  --output ./am_txt_files")
    else:
        print("❌ НЕКОТОРЫЕ ТЕСТЫ НЕ ПРОШЛИ")
        sys.exit(1)
